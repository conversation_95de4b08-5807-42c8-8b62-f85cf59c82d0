<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\Booking;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ESewaService
{
    private $merchantId;
    private $secretKey;
    private $baseUrl;

    public function __construct()
    {
        $this->merchantId = config('services.esewa.merchant_id', 'EPAYTEST');
        $this->secretKey = config('services.esewa.secret_key', '8gBm/:&EnhH.1/q');
        $this->baseUrl = config('services.esewa.base_url', 'https://uat.esewa.com.np');
    }

    /**
     * Generate payment form data for eSewa
     */
    public function generatePaymentForm(Booking $booking, Payment $payment)
    {
        $amount = $booking->total_amount;
        $taxAmount = 0;
        $serviceCharge = 0;
        $deliveryCharge = 0;
        $totalAmount = $amount + $taxAmount + $serviceCharge + $deliveryCharge;

        $successUrl = route('payments.esewa.success');
        $failureUrl = route('payments.esewa.failure');

        return [
            'tAmt' => $totalAmount,
            'amt' => $amount,
            'txAmt' => $taxAmount,
            'psc' => $serviceCharge,
            'pdc' => $deliveryCharge,
            'scd' => $this->merchantId,
            'pid' => $payment->transaction_id,
            'su' => $successUrl,
            'fu' => $failureUrl,
        ];
    }

    /**
     * Verify payment with eSewa
     */
    public function verifyPayment($transactionId, $amount, $referenceId)
    {
        try {
            $response = Http::post($this->baseUrl . '/epay/transrec', [
                'amt' => $amount,
                'rid' => $referenceId,
                'pid' => $transactionId,
                'scd' => $this->merchantId,
            ]);

            if ($response->successful()) {
                $responseBody = $response->body();
                
                // eSewa returns XML response for verification
                if (strpos($responseBody, 'Success') !== false) {
                    return [
                        'status' => 'success',
                        'message' => 'Payment verified successfully',
                        'data' => $responseBody
                    ];
                } else {
                    return [
                        'status' => 'failed',
                        'message' => 'Payment verification failed',
                        'data' => $responseBody
                    ];
                }
            }

            return [
                'status' => 'failed',
                'message' => 'Unable to verify payment with eSewa',
                'data' => null
            ];

        } catch (\Exception $e) {
            Log::error('eSewa payment verification failed: ' . $e->getMessage());
            
            return [
                'status' => 'error',
                'message' => 'Payment verification error: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Get eSewa payment URL
     */
    public function getPaymentUrl()
    {
        return $this->baseUrl . '/epay/main';
    }

    /**
     * Generate signature for payment verification
     */
    private function generateSignature($data)
    {
        $message = implode(',', $data);
        return hash_hmac('sha256', $message, $this->secretKey);
    }

    /**
     * Process successful payment callback
     */
    public function handleSuccessCallback($request)
    {
        $transactionId = $request->get('oid');
        $amount = $request->get('amt');
        $referenceId = $request->get('refId');

        // Find the payment record
        $payment = Payment::where('transaction_id', $transactionId)->first();
        
        if (!$payment) {
            return [
                'status' => 'error',
                'message' => 'Payment record not found'
            ];
        }

        // Verify payment with eSewa
        $verification = $this->verifyPayment($transactionId, $amount, $referenceId);
        
        if ($verification['status'] === 'success') {
            // Update payment status
            $payment->update([
                'status' => 'success',
                'gateway_transaction_id' => $referenceId,
                'gateway_response' => $verification['data'],
                'paid_at' => now(),
            ]);

            // Update booking payment status
            $payment->booking->update([
                'payment_status' => 'paid'
            ]);

            return [
                'status' => 'success',
                'message' => 'Payment processed successfully',
                'payment' => $payment
            ];
        }

        return $verification;
    }

    /**
     * Process failed payment callback
     */
    public function handleFailureCallback($request)
    {
        $transactionId = $request->get('pid');
        
        // Find the payment record
        $payment = Payment::where('transaction_id', $transactionId)->first();
        
        if ($payment) {
            $payment->update([
                'status' => 'failed',
                'gateway_response' => 'Payment failed or cancelled by user',
            ]);
        }

        return [
            'status' => 'failed',
            'message' => 'Payment was cancelled or failed',
            'payment' => $payment
        ];
    }
}
