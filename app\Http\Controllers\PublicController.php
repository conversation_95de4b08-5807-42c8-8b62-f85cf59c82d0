<?php

namespace App\Http\Controllers;

use App\Models\Operator;
use App\Models\Bus;
use App\Models\Trip;
use Illuminate\Http\Request;

class PublicController extends Controller
{
    /**
     * Display all operators
     */
    public function operators()
    {
        $operators = Operator::with(['user', 'buses'])
            ->whereHas('user', function($query) {
                $query->where('status', 'verified');
            })
            ->paginate(12);

        return view('public.operators.index', compact('operators'));
    }

    /**
     * Display buses for a specific operator
     */
    public function operatorBuses(Operator $operator)
    {
        // Check if operator is verified
        if ($operator->user->status !== 'verified') {
            abort(404, 'Operator not found');
        }

        $buses = $operator->buses()
            ->with(['trips' => function($query) {
                $query->with(['route.fromCity', 'route.toCity'])
                      ->where('departure_datetime', '>=', now())
                      ->orderBy('departure_datetime');
            }])
            ->paginate(12);

        return view('public.operators.buses', compact('operator', 'buses'));
    }

    /**
     * Display trips for a specific bus
     */
    public function busTrips(Bus $bus)
    {
        // Check if operator is verified
        if ($bus->operator->user->status !== 'verified') {
            abort(404, 'Bus not found');
        }

        $trips = $bus->trips()
            ->with(['route.fromCity', 'route.toCity'])
            ->where('departure_datetime', '>=', now()->subDay()) // Show trips from yesterday onwards
            ->orderBy('departure_datetime')
            ->paginate(15);

        return view('public.buses.trips', compact('bus', 'trips'));
    }
}
