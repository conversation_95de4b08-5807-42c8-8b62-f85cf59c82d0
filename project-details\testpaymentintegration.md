## Test Login Credentials
# Credentials
* Here are the test credentials & details which are required for developing environment.For production environment we will provide live credentials after successful test transactions.
eSewa ID & Password:
To make payment with eSewa sdk. One must be a registered eSewa user. For testing phase, the client/merchant can use the following eSewa id and password:

eSewa ID: 9806800001/2/3/4/5
Password: Nepal@123
MPIN: 1122 (for application only)
Merchant ID/Service Code: EPAYTEST
Token:123456

For Epay-v2:
Secret Key:8gBm/:&EnhH.1/q

For SDK Integration:
client_id:JB0BBQ4aD0UqIThFJwAKBgAXEUkEGQUBBAwdOgABHD4DChwUAB0R
client_secret:BhwIWQQADhIYSxILExMcAgFXFhcOBwAKBgAXEQ==

