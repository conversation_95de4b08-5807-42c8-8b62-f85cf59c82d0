<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <title>Trip Details | BooknGo</title>
        <meta name="description" content="View trip details and manage bookings">

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700" rel="stylesheet" />

        <!-- Styles / Scripts -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    </head>
    <body class="bg-gray-50 font-sans antialiased">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center">
                        <a href="<?php echo e(url('/')); ?>" class="text-2xl font-bold text-blue-600">BooknGo</a>
                        <span class="ml-2 text-sm text-gray-500">Trip Details</span>
                    </div>
                    <nav class="flex items-center space-x-4">
                        <a href="<?php echo e(route('trips.index')); ?>" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out">
                            Back to Trips
                        </a>
                        <a href="<?php echo e(route('dashboard')); ?>" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out">
                            Dashboard
                        </a>
                    </nav>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Page Header -->
            <div class="mb-8">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">
                            <?php echo e($trip->route->fromCity->name); ?> → <?php echo e($trip->route->toCity->name); ?>

                        </h1>
                        <p class="mt-2 text-gray-600">
                            <?php echo e($trip->departure_datetime->format('l, F j, Y \a\t g:i A')); ?>

                            <?php if($trip->arrival_time): ?>
                                → <?php echo e($trip->arrival_time->format('g:i A')); ?>

                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="mt-4 sm:mt-0 flex space-x-3">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $trip)): ?>
                            <a href="<?php echo e(route('trips.edit', $trip)); ?>" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Edit Trip
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo e(route('trips.select-seats', $trip)); ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            View Seats
                        </a>
                    </div>
                </div>
            </div>

            <!-- Trip Status Banner -->
            <div class="mb-8">
                <div class="rounded-lg p-4 
                    <?php if($trip->status === 'active'): ?> bg-green-50 border border-green-200
                    <?php elseif($trip->status === 'cancelled'): ?> bg-red-50 border border-red-200
                    <?php elseif($trip->status === 'completed'): ?> bg-gray-50 border border-gray-200
                    <?php else: ?> bg-blue-50 border border-blue-200 <?php endif; ?>">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <?php if($trip->status === 'active'): ?>
                                <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            <?php elseif($trip->status === 'cancelled'): ?>
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            <?php else: ?>
                                <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                </svg>
                            <?php endif; ?>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium 
                                <?php if($trip->status === 'active'): ?> text-green-800
                                <?php elseif($trip->status === 'cancelled'): ?> text-red-800
                                <?php elseif($trip->status === 'completed'): ?> text-gray-800
                                <?php else: ?> text-blue-800 <?php endif; ?>">
                                Trip Status: <?php echo e(ucfirst($trip->status)); ?>

                                <?php if($trip->status === 'active'): ?>
                                    - Accepting bookings
                                <?php elseif($trip->status === 'cancelled'): ?>
                                    - This trip has been cancelled
                                <?php elseif($trip->status === 'completed'): ?>
                                    - This trip has been completed
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Trip Information -->
                <div class="lg:col-span-2 space-y-8">
                    <!-- Route & Schedule Details -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">Route & Schedule</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Departure</h4>
                                    <div class="text-lg font-semibold text-gray-900"><?php echo e($trip->route->fromCity->name); ?></div>
                                    <div class="text-sm text-gray-600"><?php echo e($trip->departure_datetime->format('M j, Y \a\t g:i A')); ?></div>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Arrival</h4>
                                    <div class="text-lg font-semibold text-gray-900"><?php echo e($trip->route->toCity->name); ?></div>
                                    <div class="text-sm text-gray-600">
                                        <?php if($trip->arrival_time): ?>
                                            <?php echo e($trip->arrival_time->format('M j, Y \a\t g:i A')); ?>

                                        <?php else: ?>
                                            <span class="text-gray-400">Time not specified</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if($trip->route->estimated_km): ?>
                                <div class="mt-4 pt-4 border-t border-gray-200">
                                    <div class="text-sm text-gray-600">
                                        <span class="font-medium">Distance:</span> <?php echo e($trip->route->estimated_km); ?> km
                                        <?php if($trip->arrival_time): ?>
                                            <span class="ml-4 font-medium">Duration:</span> 
                                            <?php echo e($trip->departure_datetime->diffForHumans($trip->arrival_time, true)); ?>

                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Bus Information -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">Bus Information</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Bus Details</h4>
                                    <div class="text-lg font-semibold text-gray-900"><?php echo e($trip->bus->name ?: $trip->bus->registration_number); ?></div>
                                    <div class="text-sm text-gray-600"><?php echo e($trip->bus->type); ?> • <?php echo e($trip->bus->total_seats); ?> seats</div>
                                    <?php if($trip->bus->features): ?>
                                        <div class="mt-2 flex flex-wrap gap-1">
                                            <?php $__currentLoopData = $trip->bus->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    <?php echo e($feature); ?>

                                                </span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Operator</h4>
                                    <div class="text-lg font-semibold text-gray-900"><?php echo e($trip->bus->operator->user->name); ?></div>
                                    <?php if($trip->bus->operator->company_name): ?>
                                        <div class="text-sm text-gray-600"><?php echo e($trip->bus->operator->company_name); ?></div>
                                    <?php endif; ?>
                                    <?php if($trip->bus->operator->contact_info && isset($trip->bus->operator->contact_info['phone'])): ?>
                                        <div class="text-sm text-gray-600"><?php echo e($trip->bus->operator->contact_info['phone']); ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bookings List -->
                    <?php if($trip->bookings->count() > 0): ?>
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-900">Passenger Bookings</h3>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Passenger</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Booking Code</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Seats</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <?php $__currentLoopData = $trip->bookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div>
                                                        <div class="text-sm font-medium text-gray-900"><?php echo e($booking->passenger_name); ?></div>
                                                        <div class="text-sm text-gray-500"><?php echo e($booking->passenger_phone); ?></div>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    <?php echo e($booking->booking_code); ?>

                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    <?php echo e($booking->bookingSeats->pluck('seat.seat_number')->join(', ')); ?>

                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    NPR <?php echo e(number_format($booking->total_amount)); ?>

                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                        <?php if($booking->status === 'confirmed'): ?> bg-green-100 text-green-800
                                                        <?php elseif($booking->status === 'cancelled'): ?> bg-red-100 text-red-800
                                                        <?php elseif($booking->status === 'pending'): ?> bg-yellow-100 text-yellow-800
                                                        <?php else: ?> bg-blue-100 text-blue-800 <?php endif; ?>">
                                                        <?php echo e(ucfirst($booking->status)); ?>

                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Pricing Information -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">Pricing</h3>
                        </div>
                        <div class="p-6">
                            <div class="text-3xl font-bold text-gray-900">NPR <?php echo e(number_format($trip->price)); ?></div>
                            <div class="text-sm text-gray-600">per seat</div>
                            <?php if($trip->is_festival_fare): ?>
                                <div class="mt-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                        Festival Pricing Applied
                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Seat Availability -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">Seat Availability</h3>
                        </div>
                        <div class="p-6">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-blue-600"><?php echo e($trip->available_seats_count); ?></div>
                                <div class="text-sm text-gray-600">of <?php echo e($trip->bus->total_seats); ?> seats available</div>
                                
                                <div class="mt-4">
                                    <div class="w-full bg-gray-200 rounded-full h-3">
                                        <div class="bg-blue-600 h-3 rounded-full" style="width: <?php echo e(($trip->available_seats_count / $trip->bus->total_seats) * 100); ?>%"></div>
                                    </div>
                                </div>

                                <div class="mt-4 grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <div class="font-medium text-gray-900"><?php echo e($trip->bus->total_seats - $trip->available_seats_count); ?></div>
                                        <div class="text-gray-600">Booked</div>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-900"><?php echo e($trip->available_seats_count); ?></div>
                                        <div class="text-gray-600">Available</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
                        </div>
                        <div class="p-6 space-y-3">
                            <a href="<?php echo e(route('trips.select-seats', $trip)); ?>" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                View Seat Map
                            </a>
                            
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $trip)): ?>
                                <a href="<?php echo e(route('trips.edit', $trip)); ?>" class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                    Edit Trip Details
                                </a>
                            <?php endif; ?>

                            <?php if($trip->status === 'active'): ?>
                                <form method="POST" action="<?php echo e(route('trips.cancel', $trip)); ?>" class="w-full" onsubmit="return confirm('Are you sure you want to cancel this trip? This action cannot be undone.')">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                        Cancel Trip
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
<?php /**PATH C:\xampp\htdocs\BooknGo\resources\views/trips/show.blade.php ENDPATH**/ ?>